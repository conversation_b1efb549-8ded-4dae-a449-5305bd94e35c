# 本地配置文件使用说明

## 概述

为了方便本地开发，项目支持使用 `application-local.yaml` 配置文件来覆盖默认配置，而不需要修改主配置文件 `application.yaml`。

## 使用方法

1. **复制配置模板**
   ```bash
   cp application-local.yaml.template application-local.yaml
   ```
   或者直接使用已创建的 `application-local.yaml` 文件

2. **修改本地配置**
   根据你的本地环境修改 `application-local.yaml` 中的配置：
   
   - **数据库配置**：修改数据库连接信息
   - **Redis配置**：修改Redis连接信息  
   - **OSS配置**：如果使用阿里云OSS，填写你的OSS配置
   - **文件上传配置**：修改本地文件上传路径

3. **启动应用**
   应用会自动加载 `application-local.yaml` 配置文件

## 重要说明

- ✅ `application-local.yaml` 已添加到 `.gitignore`，不会被提交到仓库
- ✅ 本地配置会覆盖 `application.yaml` 中的相同配置项
- ✅ 团队成员可以各自维护自己的本地配置，互不影响
- ⚠️ 请不要在 `application-local.yaml` 中添加敏感信息到仓库

## 配置优先级

Spring Boot 配置加载优先级（高到低）：
1. `application-local.yaml` （本地配置）
2. `application.yaml` （默认配置）

## 常见配置示例

### 数据库配置
```yaml
spring:
  datasource:
    url: ********************************
    username: your_username
    password: your_password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

### 文件上传配置
```yaml
hmdp:
  blog:
    upload:
      type: local
      local:
        upload-dir: /your/local/upload/path
        url-prefix: http://localhost:8080/images
```
