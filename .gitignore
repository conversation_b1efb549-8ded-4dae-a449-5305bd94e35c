# IDE 配置文件 (IntelliJ IDEA)
.idea/
*.iml
*.iws
*.ipr
.project
.classpath
.settings/
 
# VS Code 配置文件
.vscode/
 
# Maven/Gradle 构建输出
target/
build/
.gradle/
*.log
*.jar
*.war
*.zip
*.tar.gz
*.log
!**/src/main/resources/application.properties  # 如果你的 Spring Boot 配置在 resources 下，且是提交的，保留此行
!**/src/main/resources/*.yml                 # 同上
!**/src/main/resources/*.yaml                # 同上
 
# Node.js 相关 (针对 frontend 目录)
frontend/newFront/node_modules/
frontend/newFront/dist/           # 前端打包输出目录，例如 Vue/React 项目的构建产物
frontend/newFront/npm-debug.log*
frontend/newFront/yarn-debug.log*
frontend/newFront/.cache/
frontend/newFront/public/dist/    # 某些项目可能将构建产物放在 public/dist
 
# 开发相关（根据需要添加）
.DS_Store # macOS
Thumbs.db # Windows
*.swp     # Vim 临时文件
*.swo     # Vim 临时文件
 
# 其他可能忽略的文件
*.bak
*.tmp

# 本地配置文件（不提交到仓库）
**/application-local.yaml
**/application-local.yml
**/application-local.properties