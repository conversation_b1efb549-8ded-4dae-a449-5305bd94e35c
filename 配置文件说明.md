# 配置文件说明文档

## 概述

本文档详细说明了 `application.yaml` 配置文件的结构和各个配置项的作用。配置文件已经按照功能模块进行了清晰的分组和注释。

## 配置文件结构

### 1. 服务器配置
```yaml
# ===================================================================
# 服务器配置
# ===================================================================
server:
  port: 8081  # 应用服务端口
```

### 2. Spring Boot 核心配置
```yaml
# ===================================================================
# Spring Boot 核心配置
# ===================================================================
spring:
  # 应用名称
  application:
    name: hmdp
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************
    username: root
    password: 123456
  
  # Redis配置
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123321
    lettuce:
      pool:
        max-active: 10    # 连接池最大连接数
        max-idle: 10      # 连接池最大空闲连接数
        min-idle: 1       # 连接池最小空闲连接数
        time-between-eviction-runs: 10s  # 空闲连接检测周期
  
  # JSON序列化配置
  jackson:
    default-property-inclusion: non_null  # JSON处理时忽略非空字段
```

### 3. MyBatis Plus 配置
```yaml
# ===================================================================
# MyBatis Plus 配置
# ===================================================================
mybatis-plus:
  type-aliases-package: com.hmdp.entity  # 实体类别名扫描包
```

### 4. 日志配置
```yaml
# ===================================================================
# 日志配置
# ===================================================================
logging:
  level:
    '[com.hmdp]': debug  # 设置项目包的日志级别为debug
```

### 5. 阿里云OSS配置
```yaml
# ===================================================================
# 阿里云OSS配置
# ===================================================================
sky:
  alioss:
    endpoint: oss-cn-beijing.aliyuncs.com           # OSS服务端点
    access-key-id: LTAI5tMtDM6tVFiKAbuB2wZN         # 访问密钥ID
    access-key-secret: ******************************  # 访问密钥Secret
    bucket-name: sky-take-out-01luck                 # OSS存储桶名称
```

### 6. HMDP 业务配置
```yaml
# ===================================================================
# HMDP 业务配置
# ===================================================================
hmdp:
  # 博客图片上传配置
  blog:
    upload:
      # 上传方式：local(本地存储) 或 oss(阿里云OSS)
      type: oss
      # 是否启用博客图片上传功能
      enabled: true
      
      # 本地存储配置（当type=local时使用）
      local:
        upload-dir: C:\Users\<USER>\Desktop\interview\hmqp\nginx-1.18.0\html\hmdp\imgs
        url-prefix: http://localhost:8080/hmdp/imgs
      
      # OSS存储配置（当type=oss时使用）
      oss:
        path-prefix: hmdp/blog/images
      
      # 通用配置
      max-file-size: 5242880  # 最大文件大小（字节，5MB）
      allowed-extensions:     # 允许的文件扩展名
        - jpg
        - jpeg
        - png
        - gif
        - bmp
        - webp
```

## 配置项详解

### 数据库配置
- **driver-class-name**: MySQL 8.0+ 驱动类
- **url**: 数据库连接URL，包含时区和SSL设置
- **username/password**: 数据库用户名和密码

### Redis配置
- **host/port**: Redis服务器地址和端口
- **password**: Redis密码
- **lettuce.pool**: 连接池配置，优化连接管理

### 阿里云OSS配置
- **endpoint**: OSS服务的访问域名，根据地域选择
- **access-key-id**: 阿里云访问密钥ID
- **access-key-secret**: 阿里云访问密钥Secret
- **bucket-name**: OSS存储桶名称

### 博客上传配置
- **type**: 存储方式选择
  - `oss`: 使用阿里云OSS存储
  - `local`: 使用本地文件系统存储
- **enabled**: 功能开关
  - `true`: 启用新的上传功能
  - `false`: 使用原有上传方式
- **local**: 本地存储相关配置
  - **upload-dir**: 本地存储目录路径
  - **url-prefix**: 文件访问URL前缀
- **oss**: OSS存储相关配置
  - **path-prefix**: OSS中的文件路径前缀
- **max-file-size**: 文件大小限制（字节）
- **allowed-extensions**: 允许上传的文件扩展名列表

## 环境配置建议

### 开发环境
```yaml
hmdp:
  blog:
    upload:
      type: local  # 使用本地存储，节省成本
      enabled: true
```

### 测试环境
```yaml
hmdp:
  blog:
    upload:
      type: oss    # 使用OSS，模拟生产环境
      enabled: true
```

### 生产环境
```yaml
hmdp:
  blog:
    upload:
      type: oss    # 使用OSS，保证可靠性
      enabled: true
      oss:
        path-prefix: prod/hmdp/blog/images  # 生产环境路径前缀
```

## 安全注意事项

### 1. 敏感信息保护
- 生产环境中应将数据库密码、Redis密码、OSS密钥等敏感信息外部化
- 可使用环境变量或配置中心管理敏感配置

### 2. OSS安全配置
- 定期更换AccessKey
- 使用RAM子账号，限制权限范围
- 配置OSS防盗链保护

### 3. 数据库安全
- 使用强密码
- 限制数据库访问IP
- 定期备份数据

## 配置验证

启动应用时，检查日志中是否有以下信息：

### 成功启动标志
```
开始创建阿里云OSS工具类对象
OSS Endpoint: oss-cn-beijing.aliyuncs.com
OSS Bucket: sky-take-out-01luck
开始创建博客图片上传服务对象
上传方式: oss
```

### 常见错误
1. **数据库连接失败**: 检查数据库服务是否启动，用户名密码是否正确
2. **Redis连接失败**: 检查Redis服务是否启动，密码是否正确
3. **OSS配置错误**: 检查AccessKey是否有效，Bucket是否存在

## 配置优化建议

### 1. 性能优化
- 根据实际负载调整Redis连接池大小
- 配置合适的文件大小限制
- 使用CDN加速OSS访问

### 2. 监控配置
- 启用应用监控
- 配置日志收集
- 设置告警规则

### 3. 备份策略
- 定期备份配置文件
- 建立配置变更记录
- 制定回滚方案
