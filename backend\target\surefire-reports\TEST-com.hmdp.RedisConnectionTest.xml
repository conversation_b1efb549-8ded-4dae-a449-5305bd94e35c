<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.hmdp.RedisConnectionTest" time="8.239" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\backend\target\test-classes;C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\backend\target\classes;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\data\spring-data-redis\2.6.2\spring-data-redis-2.6.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-test\5.3.31\spring-test-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-boot-starter\3.4.3\mybatis-plus-boot-starter-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus\3.4.3\mybatis-plus-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-extension\3.4.3\mybatis-plus-extension-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-core\3.4.3\mybatis-plus-core-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-annotation\3.4.3\mybatis-plus-annotation-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\github\jsqlparser\jsqlparser\4.0\jsqlparser-4.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\cn\hutool\hutool-all\5.7.17\hutool-all-5.7.17.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\redisson\redisson\3.13.6\redisson-3.13.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\reactivex\rxjava2\rxjava\2.2.21\rxjava-2.2.21.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jboss\marshalling\jboss-marshalling-river\2.0.10.Final\jboss-marshalling-river-2.0.10.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jboss\marshalling\jboss-marshalling\2.0.10.Final\jboss-marshalling-2.0.10.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.5\jackson-dataformat-yaml-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\oss\aliyun-sdk-oss\3.15.1\aliyun-sdk-oss-3.15.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\stax\stax-api\1.0.1\stax-api-1.0.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\aliyun-java-sdk-core\4.5.10\aliyun-java-sdk-core-4.5.10.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 10"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Java\jdk17\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2138402218115247737\surefirebooter17236951595983355664.jar C:\Users\<USER>\AppData\Local\Temp\surefire2138402218115247737 2025-07-29T15-12-40_891-jvmRun1 surefire4468797774919741715tmp surefire_017737262470179430779tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="RedisConnectionTest"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\backend\target\test-classes;C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\backend\target\classes;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\data\spring-data-redis\2.6.2\spring-data-redis-2.6.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-test\5.3.31\spring-test-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-boot-starter\3.4.3\mybatis-plus-boot-starter-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus\3.4.3\mybatis-plus-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-extension\3.4.3\mybatis-plus-extension-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-core\3.4.3\mybatis-plus-core-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\baomidou\mybatis-plus-annotation\3.4.3\mybatis-plus-annotation-3.4.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\github\jsqlparser\jsqlparser\4.0\jsqlparser-4.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\cn\hutool\hutool-all\5.7.17\hutool-all-5.7.17.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\redisson\redisson\3.13.6\redisson-3.13.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\reactivex\rxjava2\rxjava\2.2.21\rxjava-2.2.21.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jboss\marshalling\jboss-marshalling-river\2.0.10.Final\jboss-marshalling-river-2.0.10.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jboss\marshalling\jboss-marshalling\2.0.10.Final\jboss-marshalling-2.0.10.Final.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.5\jackson-dataformat-yaml-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\oss\aliyun-sdk-oss\3.15.1\aliyun-sdk-oss-3.15.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\stax\stax-api\1.0.1\stax-api-1.0.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\aliyun-java-sdk-core\4.5.10\aliyun-java-sdk-core-4.5.10.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\Java\Maven\apache-maven-3.8.8\maven-repo\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Java\jdk17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\backend"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2138402218115247737\surefirebooter17236951595983355664.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.12+8-LTS-286"/>
    <property name="user.name" value="lucky"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\Java\Maven\apache-maven-3.8.8\maven-repo"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.12"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\backend"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Java\jdk17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\tools\UI\Seelen UI;d:\tools\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;D:\Java\jdk17\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;D:\Java\Jdk11\bin;D:\mingwC\x86_64-8.1.0-release-win32-seh-rt_v6-rev0\bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;D:\VMware\vmware\bin\;D:\python\python\python-3.9.7-embed-amd64\Scripts;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\Java\jdk9\bin;D:\Java\Maven\apache-maven-3.8.8\bin;C:\ProgramData\Oracle\Java\javapath;E:\Dev\java\jdk9\jre\bin;E:\Dev\java\jdk9\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;D:\????????\mysqlo?????\bin;C:\Prog;am Files\Pandoc\;D:\Xshell\Xmanager 7\;D:\Xshell\Xshell 7\;D:\Xshell\Xftp 7\;D:\Xshell\Xlpd 7\;C:\Program Files\dotnet\;D:\vscode\MinGw_c\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\;D:\NodeJs;D:\Git\Git\cmd;C:\Program Files\Go\bin;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin\;E:\Dev\python\python3.9.13\Scripts\;E:\Dev\python\python3.9.13\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;D:\Java\IntelliJ IDEA 2023.3.8\bin;;D:\python\pycharm\PyCharm 2022.2.2\bin;;E:\Dev\vscode-gcc\x86_64-8.1.0-release-posix-sjlj-rt_v6-rev0\mingw64\bin;D:\vscode\Microsoft VS Code\bin;D:\MySQL\Hyper\resources\bin;C:\Users\<USER>\AppData\Local\Programs\EmEditor;E:\apps\??????\QQGameTempest\Hall.57986\;D:\python\Graphviz\bin;D:\Java\Maven\apache-maven-3.8.8-bin\apache-maven-3.8.8\bin;D:\Java\Jdk11\bin;D:\Java\IDEA\bin;;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\tools\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\MySQL\MySQL Server 8.1\bin;C:\Users\<USER>\go\bin;D:\ProJect\java\01demo\sky-take-out\wechat-dev\dll;;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.12+8-LTS-286"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testRedisConnection" classname="com.hmdp.RedisConnectionTest" time="1.417"/>
</testsuite>