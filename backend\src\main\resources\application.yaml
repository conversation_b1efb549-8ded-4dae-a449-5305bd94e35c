# ===================================================================
# 服务器配置
# ===================================================================
server:
  port: 8081

# ===================================================================
# Spring Boot 核心配置
# ===================================================================
spring:
  # 应用名称
  application:
    name: hmdp

  # 激活配置文件
  profiles:
    active: local

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************
    username: root
    password: 123456

  # Redis配置
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123321
    lettuce:
      pool:
        max-active: 10
        max-idle: 10
        min-idle: 1
        time-between-eviction-runs: 10s

  # JSON序列化配置
  jackson:
    default-property-inclusion: NON_NULL # JSON处理时忽略非空字段

# ===================================================================
# MyBatis Plus 配置
# ===================================================================
mybatis-plus:
  type-aliases-package: com.hmdp.entity # 别名扫描包

# ===================================================================
# 日志配置
# ===================================================================
logging:
  level:
    '[com.hmdp]': debug

# ===================================================================
# 阿里云OSS配置
# ===================================================================
sky:
  alioss:
    endpoint: oss-cn-beijing.aliyuncs.com
    access-key-id: LTAI5tMtDM6tVFiKAbuB2wZN
    access-key-secret: ******************************
    bucket-name: sky-take-out-01luck

# ===================================================================
# HMDP 业务配置
# ===================================================================
hmdp:
  # 博客图片上传配置
  blog:
    upload:
      # 上传方式：local(本地存储) 或 oss(阿里云OSS)
      type: oss
      # 是否启用博客图片上传功能
      enabled: true

      # 本地存储配置（当type=local时使用）
      local:
        upload-dir: C:\Users\<USER>\Desktop\interview\hmqp\nginx-1.18.0\html\hmdp\imgs
        url-prefix: http://localhost:8080/hmdp/imgs

      # OSS存储配置（当type=oss时使用）
      oss:
        path-prefix: hmdp/blog/images

      # 通用配置
      max-file-size: 5242880 # 最大文件大小（字节，5MB）
      allowed-extensions: # 允许的文件扩展名
        - jpg
        - jpeg
        - png
        - gif
        - bmp
        - webp

