# 本地开发环境配置文件
# 此文件用于覆盖 application.yaml 中的配置
# 请根据你的本地环境修改以下配置

spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************
    username: root
    password: your_password_here
  
  # Redis配置
  redis:
    host: ***********
    port: 6379
    password: ubuntuRedis
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: 100ms

# 阿里云OSS配置（如果使用OSS上传）
aliyun:
  oss:
    endpoint: your-oss-endpoint
    access-key-id: your-access-key-id
    access-key-secret: your-access-key-secret
    bucket-name: your-bucket-name

# HMDP 业务配置
hmdp:
  # 博客图片上传配置
  blog:
    upload:
      # 上传方式：local(本地存储) 或 oss(阿里云OSS)
      type: local
      # 是否启用博客图片上传功能
      enabled: true

      # 本地存储配置（当type=local时使用）
      local:
        upload-dir: C:\Users\<USER>\Desktop\interview\hmqp\nginx-1.18.0\html\hmdp\imgs
        url-prefix: http://localhost:8080/hmdp/imgs

      # OSS存储配置（当type=oss时使用）
      oss:
        path-prefix: hmdp/blog/images

      # 通用配置
      max-file-size: 5242880 # 最大文件大小（字节，5MB）
      allowed-extensions: # 允许的文件扩展名
        - jpg
        - jpeg
        - png
        - gif
        - bmp
        - webp

# 日志配置
logging:
  level:
    com.hmdp: debug
    org.springframework.web: debug
